import React, { useEffect, useState } from 'react';
import { getBrowserFingerprint } from '../utils/fingerprint';

const FingerprintTest: React.FC = () => {
  const [fingerprint, setFingerprint] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const testFingerprint = async () => {
      try {
        console.log('开始测试指纹生成...');
        const fp = await getBrowserFingerprint();
        console.log('指纹生成完成:', fp);
        setFingerprint(fp);
      } catch (err) {
        console.error('指纹生成失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    testFingerprint();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">浏览器指纹测试</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">指纹生成结果</h2>
          
          {loading && (
            <div className="text-blue-600">正在生成指纹...</div>
          )}
          
          {error && (
            <div className="text-red-600">
              <strong>错误:</strong> {error}
            </div>
          )}
          
          {fingerprint && (
            <div>
              <div className="mb-4">
                <strong>生成的指纹:</strong>
                <div className="mt-2 p-3 bg-gray-100 rounded font-mono text-sm break-all">
                  {fingerprint}
                </div>
              </div>
              
              <div className="mb-4">
                <strong>指纹长度:</strong> {fingerprint.length} 字符
              </div>
              
              <div className="mb-4">
                <strong>指纹组成部分:</strong>
                <div className="mt-2">
                  {fingerprint.split('-').map((part, index) => (
                    <div key={index} className="p-2 bg-gray-50 rounded mb-2">
                      <span className="font-semibold">部分 {index + 1}:</span> {part}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">屏幕信息</h2>
          <div className="space-y-2">
            <div><strong>屏幕宽度:</strong> {window.screen.width}px</div>
            <div><strong>屏幕高度:</strong> {window.screen.height}px</div>
            <div><strong>可用宽度:</strong> {window.screen.availWidth}px</div>
            <div><strong>可用高度:</strong> {window.screen.availHeight}px</div>
            <div><strong>色彩深度:</strong> {window.screen.colorDepth}位</div>
            <div><strong>像素深度:</strong> {window.screen.pixelDepth}位</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FingerprintTest;
