const express = require('express');
const router = express.Router();

// 接收前端发送的调试日志
router.post('/', (req, res) => {
  try {
    const { level, message, data } = req.body;
    
    // 根据日志级别输出到控制台
    switch (level) {
      case 'error':
        console.error(`[前端] ${message}`, data || '');
        break;
      case 'warn':
        console.warn(`[前端] ${message}`, data || '');
        break;
      case 'info':
        console.info(`[前端] ${message}`, data || '');
        break;
      default:
        console.log(`[前端] ${message}`, data || '');
        break;
    }
    
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('处理前端日志失败:', error);
    res.status(500).json({ error: '处理日志失败' });
  }
});

module.exports = router;
